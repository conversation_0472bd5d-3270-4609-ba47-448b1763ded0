import { Zalo, API } from "zca-js";

export default class ZaloHelper {
  private zaloClient: Zalo;
  private api: API;
  contructor() {
    const zalo = new Zalo({
      selfListen: false, // mặc định false, lắng nghe sự kiện của bản thân
      checkUpdate: true, // mặc định true, kiểm tra update
      logging: true, // mặc định true, bật/tắt log mặc định của thư viện
    });
    this.zaloClient = zalo;
  }
  async requestQR() {
    this.api = await this.zaloClient.loginQR(
      undefined,
      this.submitQR
    );
    this.api.listener.start();
  }
  async submitQR() {
    const info = this.api.getOwnId();
    console.log(`LOGINED`, info);
  }
}
