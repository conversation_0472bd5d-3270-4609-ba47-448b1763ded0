import { Zalo, API } from "zca-js";

export default class ZaloHelper {
  private zaloClient: Zalo.Zalo;
  private api?: API;
  constructor() {
    this.zaloClient = new Zalo({
      selfListen: false, // mặc định false, lắng nghe sự kiện của bản thân
      checkUpdate: true, // mặc định true, kiểm tra update
      logging: true, // mặc định true, bật/tắt log mặc định của thư viện
    });
  }

  async requestQR() {
    const self = this;
    this.api = await this.zaloClient.loginQR({}, (data: any) => {
      const info = self.api?.getOwnId();
      console.log(`LOGINED`, info);
    });
    this.api?.listener.start();
  }
}
