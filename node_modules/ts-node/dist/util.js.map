{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": ";;;;AAAA,mCAGgB;AAEhB,+BAA+B;AAC/B,+BAA+B;AAE/B,gBAAgB;AACH,QAAA,aAAa,GACxB,MAAA,sBAAiB,aAAjB,sBAAiB,cAAjB,sBAAiB,GACjB,8BAAyB,mCACxB,OAAO,CAAC,gBAAgB,CAA2B,CAAC;AAEvD;;;;GAIG;AACH,SAAgB,EAAE,CAAC,KAAyB;;IAC1C,OAAO,MAAA,QAAQ,CAAC,KAAK,CAAC,mCAAI,SAAS,CAAC;AACtC,CAAC;AAFD,gBAEC;AAED;;;;GAIG;AACH,SAAgB,MAAM,CACpB,YAAe,EACf,GAAG,OAAiB;IAEpB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACrC,MAAM,KAAK,GAAI,MAAc,CAAC,GAAG,CAAC,CAAC;YACnC,IAAI,KAAK,KAAK,SAAS;gBAAG,YAAoB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SAC7D;KACF;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAXD,wBAWC;AAED;;;;GAIG;AACH,SAAgB,KAAK,CAAC,KAAyB;IAC7C,OAAO,OAAO,KAAK,KAAK,QAAQ;QAC9B,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAC/C,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC;AAJD,sBAIC;AAED;;;GAGG;AACH,SAAgB,KAAK,CAAC,KAAyB;IAC7C,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AACnE,CAAC;AAFD,sBAEC;AAED,MAAM,kBAAkB,GAAG,GAAG,CAAC;AAC/B,MAAM,eAAe,GAAG,KAAK,CAAC;AAC9B;;;GAGG;AACH,SAAgB,gBAAgB,CAAC,KAAa;IAC5C,OAAO,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;AAC5D,CAAC;AAFD,4CAEC;AAED;;;GAGG;AACH,SAAgB,cAAc,CAAC,MAAW,EAAE,QAAgB;IAC1D,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAChE,CAAC;AAFD,wCAEC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAO,EAAiB;IAClD,MAAM,KAAK,GAAG,IAAI,GAAG,EAAQ,CAAC;IAE9B,OAAO,CAAC,GAAM,EAAK,EAAE;QACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACnB,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;YAClB,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAClB,OAAO,CAAC,CAAC;SACV;QACD,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;IACzB,CAAC,CAAC;AACJ,CAAC;AAXD,oCAWC;AAED;;;;GAIG;AACH,SAAgB,gCAAgC,CAC9C,SAAyB,EACzB,SAAiB;IAEjB,IAAI;QACF,MAAM,IAAI,GACR,OAAO,CAAC,sBAAsB,CAC/B,CAAC,OAAO,EAAE,CAAC;QACZ,IAAI;YACF,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;SAC7B;gBAAS;YACR,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,SAAS,EAAE,CAAC;SACnB;KACF;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,SAAS,CAAC,SAAS,CAAC,CAAC;KAC7B;AACH,CAAC;AAhBD,4EAgBC;AAED;;;;;;GAMG;AACH,SAAgB,+BAA+B,CAAC,cAAsB;IACpE,OAAO,SAAS,yBAAyB,CACvC,SAAiB,EACjB,wBAAiC;QAEjC,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE;YAChC,KAAK,EAAE,wBAAwB;gBAC7B,CAAC,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC;gBAC7B,CAAC,CAAC,CAAC,cAAc,CAAC;SACrB,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAXD,0EAWC;AAMD;;;;GAIG;AACH,SAAgB,8CAA8C,CAC5D,cAAkC,EAClC,sBAA0C,EAC1C,aAAiC,EACjC,SAAiB;;IAEjB,IAAI,cAAc,IAAI,IAAI;QAAE,OAAO,IAAA,cAAO,EAAC,cAAc,CAAC,CAAC;IAC3D,OAAO,MAAA,sBAAsB,aAAtB,sBAAsB,cAAtB,sBAAsB,GAAI,aAAa,mCAAI,SAAS,CAAC;IAC5D,+EAA+E;IAC/E,8FAA8F;IAC9F,0DAA0D;AAC5D,CAAC;AAXD,wGAWC;AAED,gBAAgB;AAChB,SAAgB,IAAI,CAAqC,EAAM;IAC7D,IAAI,KAAqB,CAAC;IAC1B,IAAI,GAAG,GAAG,KAAK,CAAC;IAChB,SAAS,MAAM,CAAC,GAAG,IAAoB;QACrC,IAAI,GAAG;YAAE,OAAO,KAAK,CAAC;QACtB,KAAK,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QACpB,GAAG,GAAG,IAAI,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAVD,oBAUC;AAED,gBAAgB;AAChB,SAAgB,YAAY,CAC1B,OAAe,EACf,cAAsB,EACtB,aAAsB;IAEtB,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IACpD,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;IAC7D,MAAM,KAAK,GACT,KAAK,GAAG,QAAQ;QAChB,CAAC,KAAK,KAAK,QAAQ;YACjB,CAAC,KAAK,GAAG,QAAQ,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrE,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,IAAI,aAAa,EAAE;QACjB,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;QACzD,IAAI;YACF,KAAK,GAAG,OAAO;gBACf,CAAC,KAAK,KAAK,OAAO;oBAChB,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC,KAAK,KAAK,OAAO,IAAI,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;KAClE;IACD,OAAO,KAAK,IAAI,IAAI,CAAC;IAErB,SAAS,KAAK,CAAC,WAAmB;QAChC,OAAO,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAxBD,oCAwBC", "sourcesContent": ["import {\n  createRequire as node<PERSON>reateRequire,\n  createRequireFromPath as node<PERSON>reateRequireFromPath,\n} from 'module';\nimport type _createRequire from 'create-require';\nimport * as ynModule from 'yn';\nimport { dirname } from 'path';\n\n/** @internal */\nexport const createRequire =\n  nodeCreateRequire ??\n  nodeCreateRequireFromPath ??\n  (require('create-require') as typeof _createRequire);\n\n/**\n * Wrapper around yn module that returns `undefined` instead of `null`.\n * This is implemented by yn v4, but we're staying on v3 to avoid v4's node 10 requirement.\n * @internal\n */\nexport function yn(value: string | undefined) {\n  return ynModule(value) ?? undefined;\n}\n\n/**\n * Like `Object.assign`, but ignores `undefined` properties.\n *\n * @internal\n */\nexport function assign<T extends object>(\n  initialValue: T,\n  ...sources: Array<T>\n): T {\n  for (const source of sources) {\n    for (const key of Object.keys(source)) {\n      const value = (source as any)[key];\n      if (value !== undefined) (initialValue as any)[key] = value;\n    }\n  }\n  return initialValue;\n}\n\n/**\n * Split a string array of values\n * and remove empty strings from the resulting array.\n * @internal\n */\nexport function split(value: string | undefined) {\n  return typeof value === 'string'\n    ? value.split(/ *, */g).filter((v) => v !== '')\n    : undefined;\n}\n\n/**\n * Parse a string as JSON.\n * @internal\n */\nexport function parse(value: string | undefined): object | undefined {\n  return typeof value === 'string' ? JSON.parse(value) : undefined;\n}\n\nconst directorySeparator = '/';\nconst backslashRegExp = /\\\\/g;\n/**\n * Replace backslashes with forward slashes.\n * @internal\n */\nexport function normalizeSlashes(value: string): string {\n  return value.replace(backslashRegExp, directorySeparator);\n}\n\n/**\n * Safe `hasOwnProperty`\n * @internal\n */\nexport function hasOwnProperty(object: any, property: string): boolean {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\n/**\n * Cached fs operation wrapper.\n */\nexport function cachedLookup<T, R>(fn: (arg: T) => R): (arg: T) => R {\n  const cache = new Map<T, R>();\n\n  return (arg: T): R => {\n    if (!cache.has(arg)) {\n      const v = fn(arg);\n      cache.set(arg, v);\n      return v;\n    }\n    return cache.get(arg)!;\n  };\n}\n\n/**\n * @internal\n * Require something with v8-compile-cache, which should make subsequent requires faster.\n * Do lots of error-handling so that, worst case, we require without the cache, and users are not blocked.\n */\nexport function attemptRequireWithV8CompileCache(\n  requireFn: typeof require,\n  specifier: string\n) {\n  try {\n    const v8CC = (\n      require('v8-compile-cache-lib') as typeof import('v8-compile-cache-lib')\n    ).install();\n    try {\n      return requireFn(specifier);\n    } finally {\n      v8CC?.uninstall();\n    }\n  } catch (e) {\n    return requireFn(specifier);\n  }\n}\n\n/**\n * Helper to discover dependencies relative to a user's project, optionally\n * falling back to relative to ts-node.  This supports global installations of\n * ts-node, for example where someone does `#!/usr/bin/env -S ts-node --swc` and\n * we need to fallback to a global install of @swc/core\n * @internal\n */\nexport function createProjectLocalResolveHelper(localDirectory: string) {\n  return function projectLocalResolveHelper(\n    specifier: string,\n    fallbackToTsNodeRelative: boolean\n  ) {\n    return require.resolve(specifier, {\n      paths: fallbackToTsNodeRelative\n        ? [localDirectory, __dirname]\n        : [localDirectory],\n    });\n  };\n}\n/** @internal */\nexport type ProjectLocalResolveHelper = ReturnType<\n  typeof createProjectLocalResolveHelper\n>;\n\n/**\n * Used as a reminder of all the factors we must consider when finding project-local dependencies and when a config file\n * on disk may or may not exist.\n * @internal\n */\nexport function getBasePathForProjectLocalDependencyResolution(\n  configFilePath: string | undefined,\n  projectSearchDirOption: string | undefined,\n  projectOption: string | undefined,\n  cwdOption: string\n) {\n  if (configFilePath != null) return dirname(configFilePath);\n  return projectSearchDirOption ?? projectOption ?? cwdOption;\n  // TODO technically breaks if projectOption is path to a file, not a directory,\n  // and we attempt to resolve relative specifiers.  By the time we resolve relative specifiers,\n  // should have configFilePath, so not reach this codepath.\n}\n\n/** @internal */\nexport function once<Fn extends (...args: any[]) => any>(fn: Fn) {\n  let value: ReturnType<Fn>;\n  let ran = false;\n  function onceFn(...args: Parameters<Fn>): ReturnType<Fn> {\n    if (ran) return value;\n    value = fn(...args);\n    ran = true;\n    return value;\n  }\n  return onceFn;\n}\n\n/** @internal */\nexport function versionGteLt(\n  version: string,\n  gteRequirement: string,\n  ltRequirement?: string\n) {\n  const [major, minor, patch, extra] = parse(version);\n  const [gteMajor, gteMinor, gtePatch] = parse(gteRequirement);\n  const isGte =\n    major > gteMajor ||\n    (major === gteMajor &&\n      (minor > gteMinor || (minor === gteMinor && patch >= gtePatch)));\n  let isLt = true;\n  if (ltRequirement) {\n    const [ltMajor, ltMinor, ltPatch] = parse(ltRequirement);\n    isLt =\n      major < ltMajor ||\n      (major === ltMajor &&\n        (minor < ltMinor || (minor === ltMinor && patch < ltPatch)));\n  }\n  return isGte && isLt;\n\n  function parse(requirement: string) {\n    return requirement.split(/[\\.-]/).map((s) => parseInt(s, 10));\n  }\n}\n"]}