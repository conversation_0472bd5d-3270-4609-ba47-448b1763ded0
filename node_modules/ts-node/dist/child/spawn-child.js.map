{"version": 3, "file": "spawn-child.js", "sourceRoot": "", "sources": ["../../src/child/spawn-child.ts"], "names": [], "mappings": ";;;AACA,iDAAsC;AACtC,6BAAoC;AACpC,kCAAuC;AACvC,iDAAqD;AAErD;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,KAAqB;IAC/C,IAAI,CAAC,IAAA,mBAAY,EAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE;QACnD,MAAM,IAAI,KAAK,CACb,0EAA0E,CAC3E,CAAC;KACH;IACD,MAAM,KAAK,GAAG,IAAA,qBAAK,EACjB,OAAO,CAAC,QAAQ,EAChB;QACE,WAAW;QACX,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC;QACrC,UAAU;QACV,gFAAgF;QAChF,IAAA,mBAAa,EAAC,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,CAAC,QAAQ,EAAE;QACnE,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC;QACxC,GAAG,wBAAS,GAAG,IAAA,uBAAQ,EAAC,KAAK,CAAC,EAAE;QAChC,GAAG,KAAK,CAAC,eAAe,CAAC,QAAQ;KAClC,EACD;QACE,KAAK,EAAE,SAAS;QAChB,KAAK,EAAE,OAAO,CAAC,KAAK;KACrB,CACF,CAAC;IACF,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;QAC1B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;QACxB,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;QAC1C,OAAO,CAAC,QAAQ,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9C,CAAC,CAAC,CAAC;IACH,0DAA0D;IAC1D,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;IACxC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IACzC,SAAS,iBAAiB,CAAC,MAAc;QACvC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC;AACH,CAAC;AAvCD,kCAuCC", "sourcesContent": ["import type { BootstrapState } from '../bin';\nimport { spawn } from 'child_process';\nimport { pathToFileURL } from 'url';\nimport { versionGteLt } from '../util';\nimport { argPrefix, compress } from './argv-payload';\n\n/**\n * @internal\n * @param state Bootstrap state to be transferred into the child process.\n * @param targetCwd Working directory to be preserved when transitioning to\n *   the child process.\n */\nexport function callInChild(state: BootstrapState) {\n  if (!versionGteLt(process.versions.node, '12.17.0')) {\n    throw new Error(\n      '`ts-node-esm` and `ts-node --esm` require node version 12.17.0 or newer.'\n    );\n  }\n  const child = spawn(\n    process.execPath,\n    [\n      '--require',\n      require.resolve('./child-require.js'),\n      '--loader',\n      // Node on Windows doesn't like `c:\\` absolute paths here; must be `file:///c:/`\n      pathToFileURL(require.resolve('../../child-loader.mjs')).toString(),\n      require.resolve('./child-entrypoint.js'),\n      `${argPrefix}${compress(state)}`,\n      ...state.parseArgvResult.restArgs,\n    ],\n    {\n      stdio: 'inherit',\n      argv0: process.argv0,\n    }\n  );\n  child.on('error', (error) => {\n    console.error(error);\n    process.exit(1);\n  });\n  child.on('exit', (code) => {\n    child.removeAllListeners();\n    process.off('SIGINT', sendSignalToChild);\n    process.off('SIGTERM', sendSignalToChild);\n    process.exitCode = code === null ? 1 : code;\n  });\n  // Ignore sigint and sigterm in parent; pass them to child\n  process.on('SIGINT', sendSignalToChild);\n  process.on('SIGTERM', sendSignalToChild);\n  function sendSignalToChild(signal: string) {\n    process.kill(child.pid, signal);\n  }\n}\n"]}