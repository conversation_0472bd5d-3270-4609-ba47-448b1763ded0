{"name": "zalo", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.11.0", "prisma": "^6.9.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "dependencies": {"@prisma/client": "^6.9.0", "express": "^5.1.0", "qrcode-terminal": "^0.12.0", "zca-js": "^2.0.0-beta.24"}}